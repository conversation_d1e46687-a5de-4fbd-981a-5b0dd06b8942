version: '3.8'

services:
  # Streamlit Web 应用服务
  web:
    build: .
    image: tradingagents-cn:latest
    container_name: TradingAgents-web
    ports:
      - "8501:8501"
    volumes:
      - .env:/app/.env
      # 开发环境代码映射
      - ./web:/app/web
      - ./tradingagents:/app/tradingagents
      - ./scripts:/app/scripts
      # 日志目录映射 - 重要：将容器内日志映射到当前项目的logs目录
      - ./logs:/app/logs
    env_file:
      - .env
    environment:
      PYTHONUNBUFFERED: 1
      PYTHONDONTWRITEBYTECODE: 1
      # 时区配置
      TZ: "Asia/Shanghai"
      # 日志配置
      TRADINGAGENTS_LOG_LEVEL: "INFO"
      TRADINGAGENTS_LOG_DIR: "/app/logs"
      TRADINGAGENTS_LOG_FILE: "/app/logs/tradingagents.log"
      TRADINGAGENTS_LOG_MAX_SIZE: "100MB"
      TRADINGAGENTS_LOG_BACKUP_COUNT: "5"
      # Docker专用数据库配置（覆盖.env中的本地配置）
      TRADINGAGENTS_MONGODB_URL: **********************************************/tradingagents?authSource=admin
      TRADINGAGENTS_REDIS_URL: redis://:tradingagents123@redis:6379
      TRADINGAGENTS_CACHE_TYPE: redis
      # Docker环境标识和PDF支持
      DOCKER_CONTAINER: "true"
      DISPLAY: ":99"
    command: /usr/local/bin/start-xvfb.sh python -m streamlit run web/app.py --server.address=0.0.0.0 --server.port=8501
    depends_on:
      - mongodb
      - redis
    networks:
      - tradingagents-network
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # 重启策略
    restart: unless-stopped
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # MongoDB数据库服务
  mongodb:
    image: mongo:4.4
    container_name: tradingagents-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: tradingagents123
      MONGO_INITDB_DATABASE: tradingagents
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - tradingagents-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:latest
    container_name: tradingagents-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass tradingagents123
    volumes:
      - redis_data:/data
    networks:
      - tradingagents-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis Commander管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: tradingagents-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:tradingagents123
    networks:
      - tradingagents-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8081"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Mongo Express管理界面 (可选)
  mongo-express:
    image: mongo-express:latest
    container_name: tradingagents-mongo-express
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: tradingagents123
      ME_CONFIG_MONGODB_URL: **********************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: tradingagents123
    networks:
      - tradingagents-network
    depends_on:
      - mongodb
    profiles:
      - management  # 使用 --profile management 启动

# 数据卷定义
volumes:
  mongodb_data:
    driver: local
    name: tradingagents_mongodb_data
  redis_data:
    driver: local
    name: tradingagents_redis_data

# 网络定义
networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network
